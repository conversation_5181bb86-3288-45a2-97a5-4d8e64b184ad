"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  FileText, 
  RefreshCw, 
  Download,
  Search,
  Filter,
  Eye,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";

interface LogStats {
  log_directory: string;
  max_total_size_mb: number;
  current_total_size_mb: number;
  files: Array<{
    name: string;
    size_mb: number;
    modified: string;
  }>;
}

interface LogData {
  log_type: string;
  total_lines: number;
  returned_lines: number;
  lines: string[];
}

const LOG_TYPES = [
  { value: "llm_communication", label: "LLM Communicatie", icon: "🤖" },
  { value: "websocket_communication", label: "WebSocket", icon: "🔌" },
  { value: "api_requests", label: "API Requests", icon: "🌐" },
  { value: "errors", label: "Errors", icon: "❌" }
];

export function LogViewer() {
  const [selectedLogType, setSelectedLogType] = useState("llm_communication");
  const [logData, setLogData] = useState<LogData | null>(null);
  const [logStats, setLogStats] = useState<LogStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [lineCount, setLineCount] = useState(100);
  const [autoRefresh, setAutoRefresh] = useState(false);

  useEffect(() => {
    loadLogStats();
    loadLogs();
  }, [selectedLogType, lineCount]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        loadLogs();
        loadLogStats();
      }, 5000); // Refresh every 5 seconds
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh, selectedLogType, lineCount]);

  const loadLogStats = async () => {
    try {
      const response = await fetch("http://localhost:8001/logs/stats");
      if (response.ok) {
        const stats = await response.json();
        setLogStats(stats);
      }
    } catch (error) {
      console.error("Error loading log stats:", error);
      toast.error("Kon log statistieken niet laden");
    }
  };

  const loadLogs = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `http://localhost:8001/logs/${selectedLogType}?lines=${lineCount}`
      );
      if (response.ok) {
        const data = await response.json();
        setLogData(data);
      } else {
        throw new Error("Failed to load logs");
      }
    } catch (error) {
      console.error("Error loading logs:", error);
      toast.error("Kon logs niet laden");
    } finally {
      setLoading(false);
    }
  };

  const downloadLogs = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/logs/${selectedLogType}?lines=10000`
      );
      if (response.ok) {
        const data = await response.json();
        const content = data.lines.join('\n');
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${selectedLogType}_${new Date().toISOString().split('T')[0]}.log`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        toast.success("Logs gedownload");
      }
    } catch (error) {
      console.error("Error downloading logs:", error);
      toast.error("Kon logs niet downloaden");
    }
  };

  const getLogLevelIcon = (line: string) => {
    if (line.includes("ERROR")) return <XCircle className="w-4 h-4 text-red-500" />;
    if (line.includes("WARNING")) return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    if (line.includes("INFO")) return <Info className="w-4 h-4 text-blue-500" />;
    if (line.includes("DEBUG")) return <Eye className="w-4 h-4 text-gray-500" />;
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getLogLevelColor = (line: string) => {
    if (line.includes("ERROR")) return "text-red-600 dark:text-red-400";
    if (line.includes("WARNING")) return "text-yellow-600 dark:text-yellow-400";
    if (line.includes("INFO")) return "text-blue-600 dark:text-blue-400";
    if (line.includes("DEBUG")) return "text-gray-600 dark:text-gray-400";
    return "text-green-600 dark:text-green-400";
  };

  const filteredLines = logData?.lines.filter(line =>
    searchQuery === "" || line.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const formatFileSize = (sizeMb) => {
    // Controleer of sizeMb een geldige waarde is
    if (sizeMb === undefined || sizeMb === null) return "0 MB";
    
    // Zorg ervoor dat sizeMb een nummer is
    const size = Number(sizeMb);
    
    // Controleer of het een geldig nummer is
    if (isNaN(size)) return "0 MB";
    
    // Format op basis van grootte
    if (size < 1) return `${(size * 1024).toFixed(1)} KB`;
    return `${size.toFixed(1)} MB`;
  };

  return (
    <div className="h-full bg-card/30 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <FileText className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">Log Viewer</h3>
            {autoRefresh && (
              <Badge variant="outline" className="text-xs">
                Auto-refresh
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              <RefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={downloadLogs}
            >
              <Download className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadLogs}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <div>
            <label className="block text-sm font-medium mb-1">Log Type</label>
            <Select value={selectedLogType} onValueChange={setSelectedLogType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {LOG_TYPES.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <span className="flex items-center space-x-2">
                      <span>{type.icon}</span>
                      <span>{type.label}</span>
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Aantal regels</label>
            <Select value={lineCount.toString()} onValueChange={(value) => setLineCount(parseInt(value))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="50">50 regels</SelectItem>
                <SelectItem value="100">100 regels</SelectItem>
                <SelectItem value="500">500 regels</SelectItem>
                <SelectItem value="1000">1000 regels</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Zoeken</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Zoek in logs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </div>

        {/* Stats */}
        {logStats && (
          <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-3">
            <Card className="p-3">
              <div className="text-xs text-muted-foreground">Totale grootte</div>
              <div className="font-medium">
                {formatFileSize(logStats.current_total_size_mb)} / {formatFileSize(logStats.max_total_size_mb)}
              </div>
            </Card>
            <Card className="p-3">
              <div className="text-xs text-muted-foreground">Log bestanden</div>
              <div className="font-medium">{logStats.files.length}</div>
            </Card>
            <Card className="p-3">
              <div className="text-xs text-muted-foreground">Huidige log</div>
              <div className="font-medium">{logData?.total_lines || 0} regels</div>
            </Card>
            <Card className="p-3">
              <div className="text-xs text-muted-foreground">Gefilterd</div>
              <div className="font-medium">{filteredLines.length} regels</div>
            </Card>
          </div>
        )}
      </div>

      {/* Log Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : filteredLines.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
            <FileText className="w-8 h-8 mb-2 opacity-50" />
            <p className="text-center">
              {searchQuery ? "Geen logs gevonden voor deze zoekopdracht" : "Geen logs beschikbaar"}
            </p>
          </div>
        ) : (
          <div className="space-y-1">
            {filteredLines.map((line, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.01 }}
                className={`flex items-start space-x-2 p-2 rounded text-xs font-mono hover:bg-accent/50 ${getLogLevelColor(line)}`}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {getLogLevelIcon(line)}
                </div>
                <div className="flex-1 break-all">
                  {searchQuery && line.toLowerCase().includes(searchQuery.toLowerCase()) ? (
                    <span
                      dangerouslySetInnerHTML={{
                        __html: line.replace(
                          new RegExp(`(${searchQuery})`, 'gi'),
                          '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>'
                        )
                      }}
                    />
                  ) : (
                    line
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
