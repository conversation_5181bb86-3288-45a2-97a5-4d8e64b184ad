"use client";

import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  Save,
  Edit3,
  Eye,
  Split,
  FileText,
  Undo,
  Redo,
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  Code,
  Link,
  Image,
  Heading1,
  Heading2,
  Heading3,
  Type,
  Wand2,
  MessageSquare,
  MoreHorizontal,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Editor } from "@monaco-editor/react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { toast } from "sonner";

interface SelectedFile {
  path: string;
  filename: string;
  type: string;
  size?: number;
  modified_at: string;
}

interface DocumentCanvasProps {
  content: string;
  onContentChange: (content: string) => void;
  onSave: () => void;
  websocket: WebSocket | null;
  selectedFile: SelectedFile | null;
}

type ViewMode = "edit" | "split" | "preview";

export function DocumentCanvas({
  content,
  onContentChange,
  onSave,
  websocket,
  selectedFile,
}: DocumentCanvasProps) {
  const [viewMode, setViewMode] = useState<ViewMode>("split");
  const [selectedText, setSelectedText] = useState("");
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [isRewriting, setIsRewriting] = useState(false);
  const [rewriteBuffer, setRewriteBuffer] = useState("");
  const [customInstruction, setCustomInstruction] = useState("");
  const [showCustomDialog, setShowCustomDialog] = useState(false);
  const [isProcessingLLM, setIsProcessingLLM] = useState(false);
  const [llmBuffer, setLlmBuffer] = useState("");
  const [insertMode, setInsertMode] = useState<"replace" | "append" | "cursor">(
    "replace"
  );
  const editorRef = useRef<any>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  const toolbarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === "s") {
        e.preventDefault();
        onSave();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [onSave]);

  // Handle WebSocket messages for rewriting
  useEffect(() => {
    if (!websocket) return;

    const handleMessage = (event: MessageEvent) => {
      try {
        const message = JSON.parse(event.data);

        // Handle streaming content for both rewriting and general LLM processing
        if (message.type === "stream") {
          if (isRewriting) {
            setRewriteBuffer((prev) => prev + message.content);
          } else if (isProcessingLLM) {
            setLlmBuffer((prev) => prev + message.content);
          }
        }
        // Handle completion
        else if (message.type === "complete") {
          if (message.replace_selection || isRewriting) {
            // Replace selected text with rewritten content
            if (editorRef.current && selectedText && rewriteBuffer) {
              insertTextInEditor(rewriteBuffer, "replace");
              toast.success("Tekst succesvol herschreven!");
            }
            // Reset rewrite state
            setIsRewriting(false);
            setRewriteBuffer("");
            setSelectedText("");
          } else if (isProcessingLLM && llmBuffer) {
            // Insert LLM output based on insert mode
            insertTextInEditor(llmBuffer, insertMode);
            toast.success("AI output toegevoegd aan document!");
            // Reset LLM state
            setIsProcessingLLM(false);
            setLlmBuffer("");
          }
        }
        // Handle direct content insertion from agent interface
        else if (
          message.type === "generate" &&
          message.content &&
          message.insert_mode
        ) {
          insertTextInEditor(message.content, message.insert_mode);
          toast.success("Agent output toegevoegd aan document!");
        }
        // Handle errors
        else if (message.type === "error") {
          if (isRewriting) {
            toast.error(`Fout bij herschrijven: ${message.message}`);
            setIsRewriting(false);
            setRewriteBuffer("");
          } else if (isProcessingLLM) {
            toast.error(`Fout bij AI verwerking: ${message.message}`);
            setIsProcessingLLM(false);
            setLlmBuffer("");
          }
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    websocket.addEventListener("message", handleMessage);
    return () => websocket.removeEventListener("message", handleMessage);
  }, [
    websocket,
    isRewriting,
    isProcessingLLM,
    selectedText,
    rewriteBuffer,
    llmBuffer,
    insertMode,
    onContentChange,
  ]);

  // Auto-save functionality
  useEffect(() => {
    if (!selectedFile || !content) return;

    const autoSaveTimer = setTimeout(() => {
      setIsAutoSaving(true);
      onSave();
      setTimeout(() => setIsAutoSaving(false), 1000);
    }, 30000); // Auto-save after 30 seconds of inactivity

    return () => clearTimeout(autoSaveTimer);
  }, [content, selectedFile, onSave]);

  // Helper function to insert text in editor
  const insertTextInEditor = (
    text: string,
    mode: "replace" | "append" | "cursor"
  ) => {
    if (!editorRef.current) return;

    const editor = editorRef.current;
    const model = editor.getModel();
    if (!model) return;

    let range;
    let insertText = text;

    switch (mode) {
      case "replace":
        // Replace selected text or insert at cursor
        range = editor.getSelection();
        break;
      case "append":
        // Append to end of document
        const lineCount = model.getLineCount();
        const lastLineLength = model.getLineLength(lineCount);
        range = {
          startLineNumber: lineCount,
          startColumn: lastLineLength + 1,
          endLineNumber: lineCount,
          endColumn: lastLineLength + 1,
        };
        insertText = "\n\n" + text;
        break;
      case "cursor":
        // Insert at current cursor position
        const position = editor.getPosition();
        range = {
          startLineNumber: position.lineNumber,
          startColumn: position.column,
          endLineNumber: position.lineNumber,
          endColumn: position.column,
        };
        break;
    }

    if (range) {
      model.pushEditOperations([], [{ range, text: insertText }], () => null);
      onContentChange(model.getValue());
    }
  };

  // Helper function to format selected text
  const formatSelectedText = (prefix: string, suffix: string = "") => {
    if (!editorRef.current || !selectedText) return;

    const editor = editorRef.current;
    const model = editor.getModel();
    const selection = editor.getSelection();

    if (model && selection) {
      const formattedText = `${prefix}${selectedText}${suffix}`;
      model.pushEditOperations(
        [],
        [{ range: selection, text: formattedText }],
        () => null
      );
      onContentChange(model.getValue());
    }
  };

  // Helper function to insert markdown elements
  const insertMarkdown = (markdown: string) => {
    if (!editorRef.current) return;

    const editor = editorRef.current;
    const position = editor.getPosition();
    const model = editor.getModel();

    if (model && position) {
      model.pushEditOperations(
        [],
        [
          {
            range: {
              startLineNumber: position.lineNumber,
              startColumn: position.column,
              endLineNumber: position.lineNumber,
              endColumn: position.column,
            },
            text: markdown,
          },
        ],
        () => null
      );
      onContentChange(model.getValue());
    }
  };

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;

    // Handle text selection
    editor.onDidChangeCursorSelection((e: any) => {
      const selection = editor.getModel()?.getValueInRange(e.selection);
      setSelectedText(selection || "");
    });

    // Configure editor theme
    editor.updateOptions({
      theme: "vs-dark",
      fontSize: 14,
      lineHeight: 1.6,
      wordWrap: "on",
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      automaticLayout: true,
    });
  };

  const handleRewriteSelection = () => {
    if (!selectedText || !websocket) {
      toast.error("Selecteer eerst tekst om te herschrijven");
      return;
    }

    if (isRewriting || isProcessingLLM) {
      toast.warning("Er wordt al een AI bewerking uitgevoerd...");
      return;
    }

    // Start rewriting process
    setIsRewriting(true);
    setRewriteBuffer("");

    const message = {
      type: "rewrite",
      content: selectedText,
      instruction:
        "Verbeter en herschrijf deze tekst. Behoud de Markdown formatting en maak de tekst duidelijker en beter leesbaar.",
      task_id: `rewrite_${Date.now()}`,
    };

    websocket.send(JSON.stringify(message));
    toast.info("AI herschrijft de geselecteerde tekst...");
  };

  const handleCustomInstruction = () => {
    if (!customInstruction.trim()) {
      toast.error("Voer een instructie in");
      return;
    }

    if (!websocket) {
      toast.error("Geen verbinding met server");
      return;
    }

    if (isRewriting || isProcessingLLM) {
      toast.warning("Er wordt al een AI bewerking uitgevoerd...");
      return;
    }

    // Start LLM processing
    setIsProcessingLLM(true);
    setLlmBuffer("");

    const message = {
      type: "generate",
      agent: "writer",
      instruction: selectedText
        ? `${customInstruction}\n\nContext tekst: ${selectedText}`
        : customInstruction,
      settings: {
        model: "deepseek-r1-distill-llama-70b",
        max_tokens: 2000,
        temperature: 0.7,
      },
      task_id: `custom_${Date.now()}`,
    };

    websocket.send(JSON.stringify(message));
    toast.info("AI verwerkt je instructie...");
    setShowCustomDialog(false);
    setCustomInstruction("");
  };

  // Undo/Redo functions
  const handleUndo = () => {
    if (editorRef.current) {
      editorRef.current.trigger("keyboard", "undo", null);
    }
  };

  const handleRedo = () => {
    if (editorRef.current) {
      editorRef.current.trigger("keyboard", "redo", null);
    }
  };

  const ViewModeButtons = () => (
    <div className="flex bg-muted rounded-lg p-1">
      <Button
        variant={viewMode === "edit" ? "default" : "ghost"}
        size="sm"
        onClick={() => setViewMode("edit")}
        className="h-8"
      >
        <Edit3 className="w-4 h-4 mr-1" />
        Bewerken
      </Button>
      <Button
        variant={viewMode === "split" ? "default" : "ghost"}
        size="sm"
        onClick={() => setViewMode("split")}
        className="h-8"
      >
        <Split className="w-4 h-4 mr-1" />
        Gesplitst
      </Button>
      <Button
        variant={viewMode === "preview" ? "default" : "ghost"}
        size="sm"
        onClick={() => setViewMode("preview")}
        className="h-8"
      >
        <Eye className="w-4 h-4 mr-1" />
        Voorvertoning
      </Button>
    </div>
  );

  return (
    <div className="h-full bg-card/30 flex flex-col">
      {/* Sticky WYSIWYG Toolbar */}
      <div
        ref={toolbarRef}
        className="sticky top-0 z-10 bg-background/95 backdrop-blur-sm border-b shadow-sm"
      >
        {/* Main Header */}
        <div className="p-3 border-b border-border/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-primary" />
                <div>
                  <h3 className="font-semibold">
                    {selectedFile?.filename || "Document Editor"}
                  </h3>
                  {selectedFile && (
                    <p className="text-xs text-muted-foreground">
                      {selectedFile.path}
                    </p>
                  )}
                </div>
                {isAutoSaving && (
                  <Badge variant="outline" className="text-xs">
                    Auto-opslaan...
                  </Badge>
                )}
                {(isRewriting || isProcessingLLM) && (
                  <Badge variant="outline" className="text-xs animate-pulse">
                    AI bezig...
                  </Badge>
                )}
              </div>

              <ViewModeButtons />
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onSave}
                disabled={!selectedFile}
              >
                <Save className="w-4 h-4 mr-2" />
                Opslaan
              </Button>
            </div>
          </div>
        </div>

        {/* Formatting Toolbar */}
        <div className="p-2 flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {/* Undo/Redo */}
            <div className="flex items-center space-x-1 mr-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleUndo}
                disabled={!selectedFile}
                className="h-8 w-8 p-0"
                title="Ongedaan maken"
              >
                <Undo className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRedo}
                disabled={!selectedFile}
                className="h-8 w-8 p-0"
                title="Opnieuw"
              >
                <Redo className="w-4 h-4" />
              </Button>
            </div>

            {/* Text Formatting */}
            <div className="flex items-center space-x-1 mr-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatSelectedText("**", "**")}
                disabled={!selectedText}
                className="h-8 w-8 p-0"
                title="Vet"
              >
                <Bold className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatSelectedText("*", "*")}
                disabled={!selectedText}
                className="h-8 w-8 p-0"
                title="Cursief"
              >
                <Italic className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatSelectedText("`", "`")}
                disabled={!selectedText}
                className="h-8 w-8 p-0"
                title="Code"
              >
                <Code className="w-4 h-4" />
              </Button>
            </div>

            {/* Headings */}
            <div className="flex items-center space-x-1 mr-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertMarkdown("# ")}
                className="h-8 w-8 p-0"
                title="Heading 1"
              >
                <Heading1 className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertMarkdown("## ")}
                className="h-8 w-8 p-0"
                title="Heading 2"
              >
                <Heading2 className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertMarkdown("### ")}
                className="h-8 w-8 p-0"
                title="Heading 3"
              >
                <Heading3 className="w-4 h-4" />
              </Button>
            </div>

            {/* Lists and Quotes */}
            <div className="flex items-center space-x-1 mr-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertMarkdown("- ")}
                className="h-8 w-8 p-0"
                title="Bullet List"
              >
                <List className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertMarkdown("1. ")}
                className="h-8 w-8 p-0"
                title="Numbered List"
              >
                <ListOrdered className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertMarkdown("> ")}
                className="h-8 w-8 p-0"
                title="Quote"
              >
                <Quote className="w-4 h-4" />
              </Button>
            </div>

            {/* Links and Images */}
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertMarkdown("[link text](url)")}
                className="h-8 w-8 p-0"
                title="Link"
              >
                <Link className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertMarkdown("![alt text](image-url)")}
                className="h-8 w-8 p-0"
                title="Image"
              >
                <Image className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* AI Actions */}
          <div className="flex items-center space-x-2">
            {selectedText && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRewriteSelection}
                disabled={isRewriting || isProcessingLLM}
                className="text-xs"
              >
                <Wand2 className="w-3 h-3 mr-1" />
                {isRewriting ? "Herschrijven..." : "Herschrijf"}
              </Button>
            )}

            <Dialog open={showCustomDialog} onOpenChange={setShowCustomDialog}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={isRewriting || isProcessingLLM}
                  className="text-xs"
                >
                  <MessageSquare className="w-3 h-3 mr-1" />
                  AI Instructie
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Custom AI Instructie</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Instructie:</label>
                    <Textarea
                      value={customInstruction}
                      onChange={(e) => setCustomInstruction(e.target.value)}
                      placeholder="Beschrijf wat je wilt dat de AI doet..."
                      rows={4}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Invoeg modus:</label>
                    <div className="flex space-x-2 mt-1">
                      <Button
                        variant={
                          insertMode === "replace" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => setInsertMode("replace")}
                      >
                        Vervang selectie
                      </Button>
                      <Button
                        variant={
                          insertMode === "cursor" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => setInsertMode("cursor")}
                      >
                        Bij cursor
                      </Button>
                      <Button
                        variant={
                          insertMode === "append" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => setInsertMode("append")}
                      >
                        Aan einde
                      </Button>
                    </div>
                  </div>
                  {selectedText && (
                    <div>
                      <label className="text-sm font-medium">
                        Geselecteerde tekst:
                      </label>
                      <div className="text-xs text-muted-foreground bg-muted p-2 rounded max-h-20 overflow-y-auto">
                        {selectedText.substring(0, 200)}
                        {selectedText.length > 200 ? "..." : ""}
                      </div>
                    </div>
                  )}
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowCustomDialog(false)}
                    >
                      Annuleren
                    </Button>
                    <Button onClick={handleCustomInstruction}>Uitvoeren</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {!selectedFile ? (
          <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
            <FileText className="w-16 h-16 mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">
              Geen bestand geselecteerd
            </h3>
            <p className="text-center max-w-md">
              Selecteer een bestand in de file explorer om te beginnen met
              bewerken, of maak een nieuw document aan.
            </p>
          </div>
        ) : (
          <>
            {viewMode === "edit" && (
              <div className="h-full">
                <Editor
                  height="100%"
                  defaultLanguage="markdown"
                  value={content}
                  onChange={(value) => onContentChange(value || "")}
                  onMount={handleEditorDidMount}
                  theme="vs-dark"
                  options={{
                    wordWrap: "on",
                    minimap: { enabled: false },
                    fontSize: 14,
                    lineHeight: 1.6,
                    padding: { top: 16, bottom: 16 },
                    fontFamily: "'JetBrains Mono', 'Fira Code', monospace",
                    scrollBeyondLastLine: false,
                    automaticLayout: true,
                  }}
                />
              </div>
            )}

            {viewMode === "preview" && (
              <div className="h-full overflow-y-auto" ref={previewRef}>
                <div className="p-6 max-w-4xl mx-auto">
                  <div className="prose prose-invert prose-lg max-w-none">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {content || "# Leeg document\n\nBegin met typen..."}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            )}

            {viewMode === "split" && (
              <ResizablePanelGroup direction="horizontal" className="h-full">
                <ResizablePanel defaultSize={50}>
                  <div className="h-full border-r">
                    <Editor
                      height="100%"
                      defaultLanguage="markdown"
                      value={content}
                      onChange={(value) => onContentChange(value || "")}
                      onMount={handleEditorDidMount}
                      theme="vs-dark"
                      options={{
                        wordWrap: "on",
                        minimap: { enabled: false },
                        fontSize: 14,
                        lineHeight: 1.6,
                        padding: { top: 16, bottom: 16 },
                        fontFamily: "'JetBrains Mono', 'Fira Code', monospace",
                        scrollBeyondLastLine: false,
                        automaticLayout: true,
                      }}
                    />
                  </div>
                </ResizablePanel>

                <ResizableHandle />

                <ResizablePanel defaultSize={50}>
                  <div className="h-full overflow-y-auto" ref={previewRef}>
                    <div className="p-6">
                      <div className="prose prose-invert prose-lg max-w-none">
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {content || "# Leeg document\n\nBegin met typen..."}
                        </ReactMarkdown>
                      </div>
                    </div>
                  </div>
                </ResizablePanel>
              </ResizablePanelGroup>
            )}
          </>
        )}
      </div>

      {/* Selection Indicator */}
      {selectedText && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="px-4 py-2 bg-primary/10 border-t border-primary/30 text-primary text-sm"
        >
          <span className="font-medium">Geselecteerd:</span> "
          {selectedText.substring(0, 50)}
          {selectedText.length > 50 ? "..." : ""}"
        </motion.div>
      )}
    </div>
  );
}
