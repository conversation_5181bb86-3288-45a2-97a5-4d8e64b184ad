"use client";

import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { Save, Edit3, Eye, Split, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { Editor } from "@monaco-editor/react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { toast } from "sonner";

interface SelectedFile {
  path: string;
  filename: string;
  type: string;
  size?: number;
  modified_at: string;
}

interface DocumentCanvasProps {
  content: string;
  onContentChange: (content: string) => void;
  onSave: () => void;
  websocket: WebSocket | null;
  selectedFile: SelectedFile | null;
}

type ViewMode = "edit" | "split" | "preview";

export function DocumentCanvas({
  content,
  onContentChange,
  onSave,
  websocket,
  selectedFile,
}: DocumentCanvasProps) {
  const [viewMode, setViewMode] = useState<ViewMode>("split");
  const [selectedText, setSelectedText] = useState("");
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [isRewriting, setIsRewriting] = useState(false);
  const [rewriteBuffer, setRewriteBuffer] = useState("");
  const editorRef = useRef<any>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === "s") {
        e.preventDefault();
        onSave();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [onSave]);

  // Handle WebSocket messages for rewriting
  useEffect(() => {
    if (!websocket) return;

    const handleMessage = (event: MessageEvent) => {
      try {
        const message = JSON.parse(event.data);

        if (message.type === "stream" && isRewriting) {
          setRewriteBuffer((prev) => prev + message.content);
        } else if (message.type === "complete" && message.replace_selection) {
          // Replace selected text with rewritten content
          if (editorRef.current && selectedText && rewriteBuffer) {
            const editor = editorRef.current;
            const model = editor.getModel();
            const selection = editor.getSelection();

            if (model && selection) {
              model.pushEditOperations(
                [],
                [
                  {
                    range: selection,
                    text: rewriteBuffer,
                  },
                ],
                () => null
              );

              // Update content
              onContentChange(model.getValue());
              toast.success("Tekst succesvol herschreven!");
            }
          }

          // Reset rewrite state
          setIsRewriting(false);
          setRewriteBuffer("");
          setSelectedText("");
        } else if (message.type === "error" && isRewriting) {
          toast.error(`Fout bij herschrijven: ${message.message}`);
          setIsRewriting(false);
          setRewriteBuffer("");
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    websocket.addEventListener("message", handleMessage);
    return () => websocket.removeEventListener("message", handleMessage);
  }, [websocket, isRewriting, selectedText, rewriteBuffer, onContentChange]);

  // Auto-save functionality
  useEffect(() => {
    if (!selectedFile || !content) return;

    const autoSaveTimer = setTimeout(() => {
      setIsAutoSaving(true);
      onSave();
      setTimeout(() => setIsAutoSaving(false), 1000);
    }, 30000); // Auto-save after 30 seconds of inactivity

    return () => clearTimeout(autoSaveTimer);
  }, [content, selectedFile, onSave]);

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;

    // Handle text selection
    editor.onDidChangeCursorSelection((e: any) => {
      const selection = editor.getModel()?.getValueInRange(e.selection);
      setSelectedText(selection || "");
    });

    // Configure editor theme
    editor.updateOptions({
      theme: "vs-dark",
      fontSize: 14,
      lineHeight: 1.6,
      wordWrap: "on",
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      automaticLayout: true,
    });
  };

  const handleRewriteSelection = () => {
    if (!selectedText || !websocket) {
      toast.error("Selecteer eerst tekst om te herschrijven");
      return;
    }

    if (isRewriting) {
      toast.warning("Er wordt al een herschrijving uitgevoerd...");
      return;
    }

    // Start rewriting process
    setIsRewriting(true);
    setRewriteBuffer("");

    const message = {
      type: "rewrite",
      content: selectedText,
      instruction:
        "Verbeter en herschrijf deze tekst. Behoud de Markdown formatting en maak de tekst duidelijker en beter leesbaar.",
      task_id: `rewrite_${Date.now()}`,
    };

    websocket.send(JSON.stringify(message));
    toast.info("AI herschrijft de geselecteerde tekst...");
  };

  const ViewModeButtons = () => (
    <div className="flex bg-muted rounded-lg p-1">
      <Button
        variant={viewMode === "edit" ? "default" : "ghost"}
        size="sm"
        onClick={() => setViewMode("edit")}
        className="h-8"
      >
        <Edit3 className="w-4 h-4 mr-1" />
        Bewerken
      </Button>
      <Button
        variant={viewMode === "split" ? "default" : "ghost"}
        size="sm"
        onClick={() => setViewMode("split")}
        className="h-8"
      >
        <Split className="w-4 h-4 mr-1" />
        Gesplitst
      </Button>
      <Button
        variant={viewMode === "preview" ? "default" : "ghost"}
        size="sm"
        onClick={() => setViewMode("preview")}
        className="h-8"
      >
        <Eye className="w-4 h-4 mr-1" />
        Voorvertoning
      </Button>
    </div>
  );

  return (
    <div className="h-full bg-card/30 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FileText className="w-5 h-5 text-primary" />
          <div>
            <h3 className="font-semibold">
              {selectedFile ? selectedFile.filename : "Document Canvas"}
            </h3>
            {selectedFile && (
              <p className="text-xs text-muted-foreground">
                {selectedFile.path}
              </p>
            )}
          </div>
          {isAutoSaving && (
            <Badge variant="outline" className="text-xs">
              Auto-opslaan...
            </Badge>
          )}
        </div>

        <div className="flex items-center space-x-3">
          <ViewModeButtons />

          {selectedText && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRewriteSelection}
              disabled={!websocket || isRewriting}
            >
              {isRewriting ? "Herschrijven..." : "Herschrijven"}
            </Button>
          )}

          <Button onClick={onSave} size="sm" disabled={!selectedFile}>
            <Save className="w-4 h-4 mr-1" />
            Opslaan
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {!selectedFile ? (
          <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
            <FileText className="w-16 h-16 mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">
              Geen bestand geselecteerd
            </h3>
            <p className="text-center max-w-md">
              Selecteer een bestand in de file explorer om te beginnen met
              bewerken, of maak een nieuw document aan.
            </p>
          </div>
        ) : (
          <>
            {viewMode === "edit" && (
              <div className="h-full">
                <Editor
                  height="100%"
                  defaultLanguage="markdown"
                  value={content}
                  onChange={(value) => onContentChange(value || "")}
                  onMount={handleEditorDidMount}
                  theme="vs-dark"
                  options={{
                    wordWrap: "on",
                    minimap: { enabled: false },
                    fontSize: 14,
                    lineHeight: 1.6,
                    padding: { top: 16, bottom: 16 },
                    fontFamily: "'JetBrains Mono', 'Fira Code', monospace",
                    scrollBeyondLastLine: false,
                    automaticLayout: true,
                  }}
                />
              </div>
            )}

            {viewMode === "preview" && (
              <div className="h-full overflow-y-auto" ref={previewRef}>
                <div className="p-6 max-w-4xl mx-auto">
                  <div className="prose prose-invert prose-lg max-w-none">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {content || "# Leeg document\n\nBegin met typen..."}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            )}

            {viewMode === "split" && (
              <ResizablePanelGroup direction="horizontal" className="h-full">
                <ResizablePanel defaultSize={50}>
                  <div className="h-full border-r">
                    <Editor
                      height="100%"
                      defaultLanguage="markdown"
                      value={content}
                      onChange={(value) => onContentChange(value || "")}
                      onMount={handleEditorDidMount}
                      theme="vs-dark"
                      options={{
                        wordWrap: "on",
                        minimap: { enabled: false },
                        fontSize: 14,
                        lineHeight: 1.6,
                        padding: { top: 16, bottom: 16 },
                        fontFamily: "'JetBrains Mono', 'Fira Code', monospace",
                        scrollBeyondLastLine: false,
                        automaticLayout: true,
                      }}
                    />
                  </div>
                </ResizablePanel>

                <ResizableHandle />

                <ResizablePanel defaultSize={50}>
                  <div className="h-full overflow-y-auto" ref={previewRef}>
                    <div className="p-6">
                      <div className="prose prose-invert prose-lg max-w-none">
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {content || "# Leeg document\n\nBegin met typen..."}
                        </ReactMarkdown>
                      </div>
                    </div>
                  </div>
                </ResizablePanel>
              </ResizablePanelGroup>
            )}
          </>
        )}
      </div>

      {/* Selection Indicator */}
      {selectedText && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="px-4 py-2 bg-primary/10 border-t border-primary/30 text-primary text-sm"
        >
          <span className="font-medium">Geselecteerd:</span> "
          {selectedText.substring(0, 50)}
          {selectedText.length > 50 ? "..." : ""}"
        </motion.div>
      )}
    </div>
  );
}
