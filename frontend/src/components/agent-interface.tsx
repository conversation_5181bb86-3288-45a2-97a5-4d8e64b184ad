"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Bot,
  Send,
  Refresh<PERSON>w,
  User,
  Loader2,
  Alert<PERSON><PERSON>gle,
  <PERSON>rk<PERSON>,
  FileEdit,
  LayoutTemplate,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

interface Settings {
  groq_api_key: string;
  model_name: string;
  max_tokens: number;
  temperature: number;
}

interface AgentInterfaceProps {
  websocket: WebSocket | null;
  onDocumentGenerated: (content: string) => void;
  settings: Settings;
}

interface ChatMessage {
  type: "user" | "assistant" | "error";
  content: string;
  timestamp: Date;
  agent?: string;
}

const agents = {
  writer: "Schrijft nieuwe content en artikelen",
  rewriter: "Verbetert en herschrijft bestaande tekst",
  structure: "Organiseert en structureert documenten",
};

const agentIcons = {
  writer: <PERSON>rk<PERSON>,
  rewriter: FileEdit,
  structure: LayoutTemplate,
};

const agentColors = {
  writer: "text-green-500",
  rewriter: "text-blue-500",
  structure: "text-purple-500",
};

export function AgentInterface({
  websocket,
  onDocumentGenerated,
  settings,
}: AgentInterfaceProps) {
  const [selectedAgent, setSelectedAgent] =
    useState<keyof typeof agents>("writer");
  const [instruction, setInstruction] = useState("");
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [streamingContent, setStreamingContent] = useState("");
  const [insertToDocument, setInsertToDocument] = useState(false);
  const chatEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!websocket) return;

    const handleMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case "stream":
            setStreamingContent((prev) => prev + data.content);
            break;

          case "complete":
            const completeMessage: ChatMessage = {
              type: "assistant",
              content: streamingContent + data.content,
              timestamp: new Date(),
              agent: selectedAgent,
            };
            setChatHistory((prev) => [...prev, completeMessage]);
            setStreamingContent("");
            setIsGenerating(false);

            // Send to document editor if option is enabled
            if (insertToDocument) {
              // Send message to document editor via WebSocket
              const documentMessage = {
                type: "generate",
                content: completeMessage.content,
                insert_mode: "cursor", // Default to cursor position
                task_id: `agent_output_${Date.now()}`,
              };
              websocket.send(JSON.stringify(documentMessage));
              toast.success("Output toegevoegd aan document!");
            } else if (data.replace_document) {
              onDocumentGenerated(completeMessage.content);
            }
            break;

          case "error":
            const errorMessage: ChatMessage = {
              type: "error",
              content: data.message || "Er is een fout opgetreden",
              timestamp: new Date(),
            };
            setChatHistory((prev) => [...prev, errorMessage]);
            setStreamingContent("");
            setIsGenerating(false);
            toast.error(data.message || "Er is een fout opgetreden");
            break;
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    websocket.addEventListener("message", handleMessage);
    return () => websocket.removeEventListener("message", handleMessage);
  }, [websocket, selectedAgent, streamingContent, onDocumentGenerated]);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatHistory, streamingContent]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!instruction.trim() || !websocket || isGenerating) return;

    if (!settings.groq_api_key) {
      toast.error("GROQ API key is niet geconfigureerd");
      return;
    }

    const userMessage: ChatMessage = {
      type: "user",
      content: instruction,
      timestamp: new Date(),
    };

    setChatHistory((prev) => [...prev, userMessage]);

    const message = {
      type: "generate",
      agent: selectedAgent,
      instruction: instruction,
      settings: {
        model: settings.model_name,
        max_tokens: settings.max_tokens,
        temperature: settings.temperature,
      },
    };

    websocket.send(JSON.stringify(message));
    setInstruction("");
    setIsGenerating(true);
    setStreamingContent("");
  };

  const clearChat = () => {
    setChatHistory([]);
    setStreamingContent("");
    setIsGenerating(false);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("nl-NL", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getAgentIcon = (agent: string) => {
    const IconComponent = agentIcons[agent as keyof typeof agentIcons] || Bot;
    return <IconComponent className="w-4 h-4" />;
  };

  const getAgentColor = (agent: string) => {
    return agentColors[agent as keyof typeof agentColors] || "text-gray-500";
  };

  return (
    <div className="h-full bg-card/30 flex flex-col border-l">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Bot className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">AI Agents</h3>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={clearChat}
            className="h-8 w-8"
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>

        {/* Agent Selector */}
        <div>
          <label className="block text-sm font-medium mb-2">
            Kies een agent:
          </label>
          <div className="grid gap-2">
            {Object.entries(agents).map(([key, description]) => {
              const IconComponent = agentIcons[key as keyof typeof agentIcons];
              const isSelected = selectedAgent === key;

              return (
                <Button
                  key={key}
                  variant={isSelected ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedAgent(key as keyof typeof agents)}
                  className={`justify-start h-auto p-3 ${
                    isSelected ? "" : "hover:bg-accent"
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <IconComponent
                      className={`w-4 h-4 mt-0.5 ${
                        isSelected
                          ? "text-primary-foreground"
                          : getAgentColor(key)
                      }`}
                    />
                    <div className="text-left">
                      <div className="font-medium capitalize">{key}</div>
                      <div
                        className={`text-xs ${
                          isSelected
                            ? "text-primary-foreground/80"
                            : "text-muted-foreground"
                        }`}
                      >
                        {description}
                      </div>
                    </div>
                  </div>
                </Button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {chatHistory.map((message, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className={`flex ${
                message.type === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <Card
                className={`max-w-[85%] p-3 ${
                  message.type === "user"
                    ? "bg-primary text-primary-foreground"
                    : message.type === "error"
                    ? "bg-destructive text-destructive-foreground"
                    : "bg-muted"
                }`}
              >
                <div className="flex items-center space-x-2 mb-1">
                  <div className="flex items-center space-x-1">
                    {message.type === "user" ? (
                      <User className="w-3 h-3" />
                    ) : message.type === "error" ? (
                      <AlertTriangle className="w-3 h-3" />
                    ) : (
                      <div
                        className={
                          message.type === "user"
                            ? "text-primary-foreground"
                            : getAgentColor(message.agent || "")
                        }
                      >
                        {getAgentIcon(message.agent || "")}
                      </div>
                    )}
                    <span className="text-xs font-medium">
                      {message.type === "user"
                        ? "Jij"
                        : message.type === "error"
                        ? "Systeem"
                        : message.agent?.charAt(0).toUpperCase() +
                          message.agent?.slice(1)}
                    </span>
                  </div>
                  <span className="text-xs opacity-70">
                    {formatTime(message.timestamp)}
                  </span>
                </div>
                <div className="text-sm whitespace-pre-wrap">
                  {message.content}
                </div>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Streaming Message */}
        {isGenerating && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <Card className="max-w-[85%] p-3 bg-muted">
              <div className="flex items-center space-x-2 mb-1">
                <div className="flex items-center space-x-1">
                  <div className={getAgentColor(selectedAgent)}>
                    {getAgentIcon(selectedAgent)}
                  </div>
                  <span className="text-xs font-medium">
                    {selectedAgent.charAt(0).toUpperCase() +
                      selectedAgent.slice(1)}
                  </span>
                </div>
                <Loader2 className="w-3 h-3 animate-spin text-primary" />
              </div>
              <div className="text-sm whitespace-pre-wrap">
                {streamingContent}
                <span className="animate-pulse">|</span>
              </div>
            </Card>
          </motion.div>
        )}

        <div ref={chatEndRef} />
      </div>

      {/* Input Form */}
      <div className="p-4 border-t">
        <form onSubmit={handleSubmit} className="space-y-3">
          <Textarea
            value={instruction}
            onChange={(e) => setInstruction(e.target.value)}
            placeholder={`Geef een opdracht aan de ${selectedAgent} agent...`}
            rows={3}
            disabled={isGenerating || !websocket}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
            className="resize-none"
          />

          {/* Insert to Document Option */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="insertToDocument"
              checked={insertToDocument}
              onChange={(e) => setInsertToDocument(e.target.checked)}
              className="rounded border-gray-300"
            />
            <label
              htmlFor="insertToDocument"
              className="text-xs text-muted-foreground"
            >
              Output direct naar document editor sturen
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-xs text-muted-foreground">
              Enter om te verzenden, Shift+Enter voor nieuwe regel
            </div>
            <Button
              type="submit"
              disabled={!instruction.trim() || isGenerating || !websocket}
              size="sm"
            >
              {isGenerating ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </form>

        {!settings.groq_api_key && (
          <div className="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-4 h-4 text-yellow-500" />
              <span className="text-xs text-yellow-600 dark:text-yellow-400">
                API key vereist voor AI functies
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
