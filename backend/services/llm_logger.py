"""
LLM Logger Service - Uitgebreide logging voor LLM communicatie
"""

import json
import logging
import os
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from threading import Lock

class LLMLogger:
    """Logger voor LLM communicatie met FIFO rotatie"""

    def __init__(self, log_dir: str = "logs", max_size_mb: int = 100):
        self.log_dir = Path(log_dir)
        self.max_size_bytes = max_size_mb * 1024 * 1024  # Convert MB to bytes
        self.lock = Lock()

        # Create log directory
        self.log_dir.mkdir(exist_ok=True)

        # Log files
        self.llm_log_file = self.log_dir / "llm_communication.log"
        self.websocket_log_file = self.log_dir / "websocket_communication.log"
        self.api_log_file = self.log_dir / "api_requests.log"
        self.error_log_file = self.log_dir / "errors.log"

        # Setup loggers
        self._setup_loggers()

    def _setup_loggers(self):
        """Setup individual loggers for different types"""
        # LLM Communication Logger
        self.llm_logger = logging.getLogger("llm_communication")
        self.llm_logger.setLevel(logging.INFO)
        if not self.llm_logger.handlers:
            handler = logging.FileHandler(self.llm_log_file)
            formatter = logging.Formatter('%(asctime)s | %(levelname)s | %(message)s')
            handler.setFormatter(formatter)
            self.llm_logger.addHandler(handler)

        # WebSocket Logger
        self.websocket_logger = logging.getLogger("websocket_communication")
        self.websocket_logger.setLevel(logging.INFO)
        if not self.websocket_logger.handlers:
            handler = logging.FileHandler(self.websocket_log_file)
            formatter = logging.Formatter('%(asctime)s | %(levelname)s | %(message)s')
            handler.setFormatter(formatter)
            self.websocket_logger.addHandler(handler)

        # API Logger
        self.api_logger = logging.getLogger("api_requests")
        self.api_logger.setLevel(logging.INFO)
        if not self.api_logger.handlers:
            handler = logging.FileHandler(self.api_log_file)
            formatter = logging.Formatter('%(asctime)s | %(levelname)s | %(message)s')
            handler.setFormatter(formatter)
            self.api_logger.addHandler(handler)

        # Error Logger
        self.error_logger = logging.getLogger("errors")
        self.error_logger.setLevel(logging.ERROR)
        if not self.error_logger.handlers:
            handler = logging.FileHandler(self.error_log_file)
            formatter = logging.Formatter('%(asctime)s | %(levelname)s | %(message)s')
            handler.setFormatter(formatter)
            self.error_logger.addHandler(handler)

    def _rotate_if_needed(self, file_path: Path):
        """Rotate log file if it exceeds max size (FIFO)"""
        if file_path.exists() and file_path.stat().st_size > self.max_size_bytes:
            # Read all lines
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Keep only the last 50% of lines (FIFO)
            keep_lines = lines[len(lines)//2:]

            # Write back
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(keep_lines)

    def log_llm_request(self, agent_type: str, model: str, system_prompt: str,
                       user_prompt: str, settings: Dict[str, Any]) -> str:
        """Log LLM request with full details"""
        request_id = str(uuid.uuid4())[:8]

        with self.lock:
            self._rotate_if_needed(self.llm_log_file)

            log_data = {
                "type": "LLM_REQUEST",
                "request_id": request_id,
                "agent_type": agent_type,
                "model": model,
                "system_prompt": system_prompt[:200] + "..." if len(system_prompt) > 200 else system_prompt,
                "user_prompt": user_prompt[:500] + "..." if len(user_prompt) > 500 else user_prompt,
                "settings": settings,
                "timestamp": datetime.now().isoformat()
            }

            self.llm_logger.info(f"LLM_REQUEST | ID: {request_id} | Agent: {agent_type} | Model: {model} | Prompt: {len(user_prompt)} chars")
            self.llm_logger.info(f"LLM_REQUEST_DETAILS | {json.dumps(log_data, ensure_ascii=False)}")

        return request_id

    def log_llm_response(self, request_id: str, response_content: Optional[str],
                        response_metadata: Optional[Dict[str, Any]] = None,
                        processing_time: float = 0, error: Optional[str] = None):
        """Log LLM response with details"""
        with self.lock:
            self._rotate_if_needed(self.llm_log_file)

            if error:
                log_data = {
                    "type": "LLM_RESPONSE_ERROR",
                    "request_id": request_id,
                    "error": error,
                    "processing_time": processing_time,
                    "timestamp": datetime.now().isoformat()
                }
                self.llm_logger.error(f"LLM_RESPONSE_ERROR | ID: {request_id} | Error: {error}")
            else:
                response_length = len(response_content) if response_content else 0
                log_data = {
                    "type": "LLM_RESPONSE",
                    "request_id": request_id,
                    "response_content": response_content[:1000] + "..." if response_content and len(response_content) > 1000 else response_content,
                    "response_length": response_length,
                    "processing_time": processing_time,
                    "metadata": response_metadata,
                    "timestamp": datetime.now().isoformat()
                }
                self.llm_logger.info(f"LLM_RESPONSE | ID: {request_id} | Length: {response_length} chars | Time: {processing_time:.2f}s")

            self.llm_logger.info(f"LLM_RESPONSE_DETAILS | {json.dumps(log_data, ensure_ascii=False)}")

    def log_llm_stream_chunk(self, request_id: str, chunk_content: str,
                           chunk_index: int, accumulated_content: str):
        """Log streaming chunk"""
        with self.lock:
            self._rotate_if_needed(self.llm_log_file)

            log_data = {
                "type": "LLM_STREAM_CHUNK",
                "request_id": request_id,
                "chunk_index": chunk_index,
                "chunk_content": chunk_content,
                "accumulated_length": len(accumulated_content),
                "timestamp": datetime.now().isoformat()
            }

            self.llm_logger.info(f"LLM_STREAM_CHUNK | ID: {request_id} | Chunk: {chunk_index} | Total: {len(accumulated_content)} chars")

    def log_websocket_message(self, direction: str, message_type: str,
                            content: Dict[str, Any], client_id: str = "unknown"):
        """Log WebSocket message"""
        with self.lock:
            self._rotate_if_needed(self.websocket_log_file)

            log_data = {
                "type": "WEBSOCKET_MESSAGE",
                "direction": direction,  # INCOMING or OUTGOING
                "message_type": message_type,
                "content": content,
                "client_id": client_id,
                "timestamp": datetime.now().isoformat()
            }

            self.websocket_logger.info(f"WEBSOCKET_{direction} | Type: {message_type} | Client: {client_id}")
            self.websocket_logger.info(f"WEBSOCKET_DETAILS | {json.dumps(log_data, ensure_ascii=False)}")

    def log_api_request(self, method: str, endpoint: str, params: Dict[str, Any] = None,
                       body: Dict[str, Any] = None, response_status: int = None,
                       processing_time: float = 0):
        """Log API request/response"""
        with self.lock:
            self._rotate_if_needed(self.api_log_file)

            log_data = {
                "type": "API_REQUEST",
                "method": method,
                "endpoint": endpoint,
                "params": params,
                "body": body,
                "response_status": response_status,
                "processing_time": processing_time,
                "timestamp": datetime.now().isoformat()
            }

            self.api_logger.info(f"API_REQUEST | {method} {endpoint} | Status: {response_status} | Time: {processing_time:.2f}s")
            self.api_logger.info(f"API_DETAILS | {json.dumps(log_data, ensure_ascii=False)}")

    def log_error(self, error_type: str, error_message: str,
                 context: Dict[str, Any] = None, stack_trace: str = None):
        """Log error with context"""
        with self.lock:
            self._rotate_if_needed(self.error_log_file)

            log_data = {
                "type": "ERROR",
                "error_type": error_type,
                "error_message": error_message,
                "context": context,
                "stack_trace": stack_trace,
                "timestamp": datetime.now().isoformat()
            }

            self.error_logger.error(f"ERROR | Type: {error_type} | Message: {error_message}")
            if stack_trace:
                self.error_logger.error(f"STACK_TRACE | {stack_trace}")
            self.error_logger.error(f"ERROR_DETAILS | {json.dumps(log_data, ensure_ascii=False)}")

    def get_log_stats(self) -> Dict[str, Any]:
        """Get statistics about log files"""
        files = []
        total_size = 0

        for log_name, log_file in [
            ("llm_communication", self.llm_log_file),
            ("websocket_communication", self.websocket_log_file),
            ("api_requests", self.api_log_file),
            ("errors", self.error_log_file)
        ]:
            if log_file.exists():
                stat = log_file.stat()
                file_size_mb = round(stat.st_size / (1024 * 1024), 2)
                total_size += file_size_mb

                files.append({
                    "name": log_file.name,
                    "size_mb": file_size_mb,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
                })

        return {
            "log_directory": str(self.log_dir),
            "max_total_size_mb": round(self.max_size_bytes / (1024 * 1024), 2),
            "current_total_size_mb": round(total_size, 2),
            "files": files
        }

    def get_recent_logs(self, log_type: str, lines: int = 50) -> List[str]:
        """Get recent log lines"""
        log_files = {
            "llm_communication": self.llm_log_file,
            "websocket_communication": self.websocket_log_file,
            "api_requests": self.api_log_file,
            "errors": self.error_log_file
        }

        log_file = log_files.get(log_type)
        if not log_file or not log_file.exists():
            return []

        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            return [line.strip() for line in all_lines[-lines:]]

# Global instance
llm_logger = LLMLogger()
